import * as vscode from 'vscode';

/**
 * Input schema for MyTool
 */
interface MyToolInput {
  message: string;
}

/**
 * Simple test Language Model Tool that echoes input
 * This is designed to be as minimal as possible to test registration
 */
export class MyTool implements vscode.LanguageModelTool<MyToolInput> {

  // Optional properties for better tool discovery
  public readonly tags: string[] = ['test', 'echo'];
  public readonly displayName: string = 'My Test Tool';

  // Required: Input schema for the tool
  public readonly inputSchema = {
    type: 'object',
    properties: {
      message: {
        type: 'string',
        description: 'The message to echo back'
      }
    },
    required: ['message']
  } as const;
  
  async prepareInvocation(
    options: vscode.LanguageModelToolInvocationPrepareOptions<MyToolInput>,
    token: vscode.CancellationToken
  ): Promise<vscode.PreparedToolInvocation> {
    
    console.log('🔧 MyTool: prepareInvocation called');
    console.log('📋 Input:', options.input);
    
    // Simple validation
    if (!options.input.message || options.input.message.trim() === '') {
      throw new Error('Message cannot be empty');
    }
    
    return {
      invocationMessage: `Echoing: "${options.input.message}"`
    };
  }

  async invoke(
    options: vscode.LanguageModelToolInvocationOptions<MyToolInput>,
    token: vscode.CancellationToken
  ): Promise<vscode.LanguageModelToolResult> {
    
    console.log('🚀 MyTool: invoke called');
    console.log('📋 Input:', options.input);
    console.log('🔑 Has toolInvocationToken:', !!options.toolInvocationToken);
    
    try {
      // Simple echo functionality
      const echoMessage = `Echo: ${options.input.message}`;
      
      console.log('✅ MyTool: Echoing message:', echoMessage);
      
      return {
        content: [new vscode.LanguageModelTextPart(echoMessage)]
      };
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('❌ Error in MyTool invoke:', error);
      
      return {
        content: [new vscode.LanguageModelTextPart(
          `❌ **MyTool Error:** ${errorMessage}`
        )]
      };
    }
  }
}

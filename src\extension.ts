import * as vscode from 'vscode';
import { DeepAgentTool } from './deep-agent-tool';

// MCP server and IPC bridge imports removed - using Language Model Tool only
// import { DeepAgentMCPServer } from './mcp-server';
// import { VSCodeIPCBridge } from './ipc-bridge';
// import { registerSessionIntegrationCommands } from './session-integration-example';

// let mcpServer: DeepAgentMCPServer | undefined;
// let ipcBridge: VSCodeIPCBridge | undefined;

export function activate(ctx: vscode.ExtensionContext): void {
  console.log('🚀 Multi-Agent Orchestrator: Extension activating...');

  try {
    // Register a test command first
    const testCommand = vscode.commands.registerCommand('deepagent.test', () => {
      vscode.window.showInformationMessage('Deep Agent extension is active!');
      console.log('🧪 Test command executed - extension is active');
    });
    ctx.subscriptions.push(testCommand);

    // Register the Language Model Tool instead of chat participant
    console.log('🔧 Registering Deep Agent as Language Model Tool...');

    try {
      const deepAgentTool = new DeepAgentTool();
      console.log('🔧 Creating tool registration...');

      const toolRegistration = vscode.lm.registerTool('deepagent', deepAgentTool);
      ctx.subscriptions.push(toolRegistration);

      console.log('✅ Language Model Tool registered successfully');
      console.log('📋 Tool name: deepagent');
      console.log('🔧 Tool registration object:', toolRegistration);
      console.log('🔧 Tool instance:', deepAgentTool);

      // Try to verify the tool is actually registered
      setTimeout(async () => {
        try {
          console.log('🔍 Checking if tool is discoverable...');

          // Check VS Code version for Language Model Tool support
          const vscodeVersion = vscode.version;
          console.log('🔍 VS Code version:', vscodeVersion);

          // Check if Language Model API is available
          console.log('🔍 Language Model API available:', !!vscode.lm);
          console.log('🔍 Language Model registerTool available:', !!vscode.lm?.registerTool);

          // Try to access the tools (this might not be available in all versions)
          try {
            if (vscode.lm && 'tools' in vscode.lm) {
              const tools = (vscode.lm as any).tools;
              console.log('🔍 Available tools count:', tools?.length || 'tools property not accessible');

              // Try to find our tool in the list
              if (tools && Array.isArray(tools)) {
                const ourTool = tools.find((tool: any) => tool.name === 'deepagent_lmt');
                if (ourTool) {
                  console.log('✅ Found our tool in tools list:', ourTool);
                } else {
                  console.log('❌ Our tool not found in tools list');
                  console.log('🔍 Available tool names:', tools.map((t: any) => t.name).slice(0, 10));
                }
              }
            }
          } catch (toolsError) {
            console.log('🔍 Cannot access tools list:', toolsError);
          }

          console.log('🔍 Tool should be available for invocation in Agent/Edit mode');
          console.log('🔍 Try typing "#deepagent" in Agent mode to test');

        } catch (checkError) {
          console.error('❌ Tool discovery check failed:', checkError);
        }
      }, 3000);

    } catch (toolError) {
      console.error('❌ Failed to register Language Model Tool:', toolError);
      if (toolError instanceof Error) {
        console.error('❌ Error details:', toolError.message, toolError.stack);
      }
      throw toolError;
    }

    console.log('✅ Multi-Agent Orchestrator: Language Model Tool registered as "deepagent"');
    console.log('📋 Tool details:', {
      name: 'deepagent',
      type: 'LanguageModelTool'
    });

    console.log('✅ Extension context:', {
      extensionPath: ctx.extensionPath,
      subscriptions: ctx.subscriptions.length
    });

    // Show a notification that the extension is active
    vscode.window.showInformationMessage('🤖 Deep Agent tool activated! Use in Agent/Edit mode or reference with #deepagent.');

    // MCP server and IPC bridge disabled - using Language Model Tool only
    console.log('🚫 MCP server and IPC bridge disabled - using Language Model Tool interface only');

    // Session integration commands disabled for Language Model Tool approach
    // registerSessionIntegrationCommands(ctx);

    // Debug: Try to see what chat participants are available
    setTimeout(async () => {
      try {
        console.log('🔍 Checking chat environment...');
        // Just log that we're checking - the actual API might not expose participant lists
        console.log('🔍 Extension should now be available as #deepagent in Agent/Edit mode');

    // Register a test command to manually verify the tool works
    const testCommand = vscode.commands.registerCommand('deepagent.testTool', async () => {
      try {
        console.log('🧪 Testing Language Model Tool manually...');

        // Create a mock invocation to test the tool
        const deepAgentTool = new DeepAgentTool();

        // Test prepareInvocation
        const prepareOptions = {
          input: { task: 'Test task: create a simple hello world function' },
          tokenBudget: 1000
        };

        const cancellationTokenSource = new vscode.CancellationTokenSource();
        const prepared = await deepAgentTool.prepareInvocation(prepareOptions, cancellationTokenSource.token);
        console.log('✅ prepareInvocation succeeded:', prepared);

        vscode.window.showInformationMessage('Language Model Tool test completed - check console for details');

      } catch (error) {
        console.error('❌ Language Model Tool test failed:', error);
        vscode.window.showErrorMessage(`Language Model Tool test failed: ${error}`);
      }
    });

    ctx.subscriptions.push(testCommand);
      } catch (debugError) {
        console.log('🔍 Debug check completed with some limitations:', debugError);
      }
    }, 2000);

  } catch (error) {
    console.error('❌ Failed to activate extension:', error);
    vscode.window.showErrorMessage(`Failed to activate Multi-Agent Orchestrator: ${error}`);
  }
}

// MCP server initialization removed - using Language Model Tool only



// Chat participant handler removed - now using Language Model Tool approach

// Unused test function removed - functionality now in Language Model Tool

export function deactivate(): void {
  console.log('🛑 Deep Agent: Extension deactivating...');
  // MCP server and IPC bridge cleanup removed - using Language Model Tool only
}
{"name": "mytool-test", "displayName": "MyTool Test", "description": "Simple Language Model Tool test extension to debug registration issues", "version": "0.0.1", "publisher": "test", "engines": {"vscode": "^1.95.0"}, "categories": ["AI", "Other"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "mytool.test", "title": "Test MyTool"}], "languageModelTools": [{"name": "mytool", "displayName": "My Test Tool", "modelDescription": "A simple test tool that echoes input to verify Language Model Tool registration works correctly. Use this tool to test if Language Model Tools are working properly in VS Code.", "userDescription": "Echo a message back to test tool functionality", "canBeReferencedInPrompt": true, "toolReferenceName": "mytool", "icon": "$(megaphone)", "tags": ["test", "echo"], "inputSchema": {"type": "object", "properties": {"message": {"type": "string", "description": "The message to echo back"}}, "required": ["message"]}}]}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "devDependencies": {"@types/node": "^18.x", "@types/vscode": "^1.95.0", "typescript": "^5.3.0"}}
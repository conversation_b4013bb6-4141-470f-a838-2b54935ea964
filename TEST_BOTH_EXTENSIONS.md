# Testing Both Extensions - MyTool vs DeepAgent

## Overview

I've created a simple `mytool` extension and applied fixes to `deepagent` to help diagnose the Language Model Tool registration issue.

## Key Changes Made to DeepAgent

### 1. **Removed Invalid Activation Event**
```json
// BEFORE
"activationEvents": [
  "onStartupFinished",
  "onLanguageModelTool"  // ← This might be invalid
]

// AFTER
"activationEvents": [
  "onStartupFinished"
]
```

### 2. **Simplified Tool Name**
```json
// BEFORE
"name": "deepagent_lmt"

// AFTER  
"name": "deepagent"
```

## Testing Steps

### Phase 1: Test MyTool (Simple Extension)

1. **Open mytool in VS Code:**
   ```bash
   cd mytool
   code .
   ```

2. **Launch Extension Development Host:**
   - Press `F5` or use "Run Extension" from Run and Debug panel
   - This opens a new VS Code window with the extension loaded

3. **Test Basic Activation:**
   - In the new window, open Command Palette (`Ctrl+Shift+P`)
   - Run "Test MyTool" command
   - Should see "MyTool extension is active!" message

4. **Test in Agent Mode:**
   - Open Copilot Chat (`Ctrl+Shift+I`)
   - Switch to **Agent mode** (not Ask mode)
   - Type: `#mytool` and see if it appears in autocomplete
   - Try: `#mytool {"message": "Hello World"}`

5. **Check Console Logs:**
   - Help > Toggle Developer Tools > Console
   - Look for mytool registration messages

### Phase 2: Test Fixed DeepAgent

1. **Open main project in VS Code:**
   ```bash
   cd ..  # Back to deep-agent root
   code .
   ```

2. **Launch Extension Development Host:**
   - Press `F5` or use "Run Extension" from Run and Debug panel

3. **Test Basic Activation:**
   - Command Palette > "Test Deep Agent Language Model Tool"
   - Should see activation message

4. **Test in Agent Mode:**
   - Open Copilot Chat (`Ctrl+Shift+I`)
   - Switch to **Agent mode**
   - Type: `#deepagent` (note: no longer `#deepagent_lmt`)
   - Try: `#deepagent {"task": "Create a simple hello world function"}`

5. **Check Console Logs:**
   - Look for "✅ Language Model Tool registered successfully"
   - Check if tool appears in tools list

## Expected Results

### MyTool Should Work
- ✅ Extension activates without errors
- ✅ Tool appears when typing `#mytool` in Agent mode
- ✅ Tool can be invoked and returns echo message
- ✅ Console shows successful registration

### DeepAgent Should Now Work
- ✅ Extension activates without the invalid activation event error
- ✅ Tool appears when typing `#deepagent` in Agent mode
- ✅ Tool can be invoked (though it may still have model/dependency issues)
- ✅ Console shows successful registration

## Debugging

### If MyTool Doesn't Work
This indicates a fundamental issue with:
- VS Code version compatibility
- Language Model Tool API availability
- Extension host environment

### If MyTool Works but DeepAgent Doesn't
This indicates the issue is in:
- Complex dependencies (`@modelcontextprotocol/sdk`, `@vscode/chat-extension-utils`)
- Model initialization code
- Tool implementation complexity

### Common Issues to Check
1. **VS Code Version**: Ensure you're using VS Code 1.95.0 or later
2. **Extension Host**: Make sure you're testing in Extension Development Host, not regular VS Code
3. **Agent Mode**: Tools only work in Agent/Edit mode, not Ask mode
4. **Console Errors**: Check for any JavaScript errors during activation

## Next Steps Based on Results

### If Both Work
- The fixes resolved the issue!
- Consider simplifying deepagent's dependencies if needed

### If Only MyTool Works
- Gradually add complexity to mytool to identify the breaking point
- Simplify deepagent's dependencies temporarily

### If Neither Works
- Check VS Code version and Language Model Tool API availability
- Verify Extension Development Host is working correctly
